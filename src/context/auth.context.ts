"use client";

import { createContext, useContext, useState } from "react";

// @context type
export type User = {
  name: string;
  email: string;
  role: string;
};
type AuthContextType = {
  user: User;
  login: (user: Omit<User, "role"> & { password: string }) => void;
  register: (user: Omit<User, "role"> & { password: string }) => void;
  logout: () => void;
  keepLogin: () => void;
};

// @initial state
const INITIAL_STATE: AuthContextType = {
  user: {
    name: "",
    email: "",
    role: "",
  },
  login: () => {},
  register: () => {},
  logout: () => {},
  keepLogin: () => {},
};

// @create context
const AuthContext = createContext<AuthContextType>(INITIAL_STATE);
const useAuth = () => useContext(AuthContext);

// @provider
const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User>(INITIAL_STATE.user);

  return (
    <AuthContext.Provider value={{
        user,
        login: (user) => setUser(user),
        register: (user) => setUser(user),
        logout: () => setUser(INITIAL_STATE.user),
        keepLogin: () => setUser(user),
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export { AuthContext, AuthProvider, useAuth };
