"use client";

import Link from "next/link";
import { LogIn } from "lucide-react";
import { Button } from "@/components/ui/button";
import { BackButton } from "@/components/feature/back-button";
import { Input } from "@/components/ui/input";
import useURLQueryParams from "@/hooks/useURLQueryParams";

export default function Login() {
  const { set, get } = useURLQueryParams();
  const email = get("email") || "";
  const password = get("password") || "";

  const handleLogin = () => {
    // Handle login logic here
  };

  return (
    <main className="flex items-center justify-center min-h-screen">
      <section className="w-2/6 h-3/5 bg-white px-6 py-4 rounded-md flex flex-col gap-4">
        <BackButton />
        <h1 className="capitalize text-2xl font-bold mb-4">Login</h1>
        <Input
          value={email}
          type="email"
          placeholder="Email"
          onChange={(e) => set("email", e.target.value)}
        />
        <Input
          value={password}
          type="password"
          placeholder="Password"
          onChange={(e) => set("password", e.target.value)}
        />
        <Button
          size="lg"
          className=" text-white cursor-pointer"
          onClick={handleLogin}
        >
          <LogIn />
          Login
        </Button>
        <p className="text-sm">
          Don&apos;t have an account?{" "}
          <Link href="/register" className="underline text-blue-700">
            Register here
          </Link>
        </p>
      </section>
    </main>
  );
}
