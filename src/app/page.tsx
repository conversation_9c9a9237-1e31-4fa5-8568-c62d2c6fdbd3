import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { LogOut, LogIn, ShieldCheck } from "lucide-react";

export default function Home() {
  return (
    <main className="flex items-center justify-center min-h-screen">
      <section className="w-2/6 h-3/5 bg-white px-6 py-4 rounded-md flex flex-col gap-4">
        <h1 className="text-2xl font-bold mb-4">Wellcome to My Website</h1>
        <p>Username: </p>
        <p>Email: </p>

        <Button className="cursor-pointer bg-red-700">
          <LogOut />
          Logout
        </Button>
        <Link href="/admin">
          <Button className="cursor-pointer w-full bg-blue-700">
            <ShieldCheck />
            Go to Admin Portal
          </Button>
        </Link>
        <Link href="/login">
          <Button className="cursor-pointer w-full">
            <LogIn />
            Login
          </Button>
        </Link>
      </section>
    </main>
  );
}
