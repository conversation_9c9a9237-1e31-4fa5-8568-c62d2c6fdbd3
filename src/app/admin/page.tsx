import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, ShieldCheck } from "lucide-react";

export default function Admin() {
  return (
    <main className="flex items-center justify-center min-h-screen">
      <section className="w-2/6 h-3/5 bg-white px-6 py-4 rounded-md flex flex-col gap-4">
        <div className="text-2xl font-bold mb-4 flex items-center gap-2">
          <ShieldCheck /> <p>Wellcome to Admin Portal</p>
        </div>
        <Link href="/">
          <Button className="cursor-pointer w-full">
            <ArrowLeft />
            Back to home
          </Button>
        </Link>
      </section>
    </main>
  );
}
